package handler

import (
	"bytes"
	"encoding/json"
	"log"
	"net/http"
	"os"

	"github.com/gorilla/websocket"
)

type ClientMessage struct {
	Message string `json:"message"`
}

type AgentSummary struct {
	Input   string `json:"input"`
	Summary string `json:"summary"`
}

type AgentResponse struct {
	SQL     string        `json:"sql,omitempty"`
	Table   interface{}   `json:"table,omitempty"`
	Summary *AgentSummary `json:"summary,omitempty"` // nested structure
	Error   string        `json:"error,omitempty"`
}

var upgrader = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool {
		return true
	},
}

func WebSocketHandler(w http.ResponseWriter, r *http.Request) {
	conn, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		log.Println("❌ WebSocket upgrade error:", err)
		return
	}
	defer conn.Close()

	for {
		var msg ClientMessage
		if err := conn.ReadJSON(&msg); err != nil {
			log.Println("❌ Read error:", err)
			break
		}

		log.Printf("📥 Message from client: %s\n", msg.Message)

		resp, err := sendToAgent(msg.Message)
		if err != nil {
			log.Println("❌ Error contacting agent:", err)
			conn.WriteJSON(map[string]string{"error": "Agent not responding"})
			continue
		}

		if err := conn.WriteJSON(resp); err != nil {
			log.Println("❌ Write error:", err)
			break
		}
	}
}

func sendToAgent(prompt string) (*AgentResponse, error) {
	agentURL := os.Getenv("AGENT_HTTP_URL")
	log.Println("📡 Sending prompt to agent at:", agentURL)

	payload, _ := json.Marshal(map[string]string{
		"message": prompt,
	})

	resp, err := http.Post(agentURL, "application/json", bytes.NewReader(payload))
	if err != nil {
		log.Println("❌ Failed to reach agent:", err)
		return nil, err
	}
	defer resp.Body.Close()

	log.Printf("✅ Agent responded with status: %s", resp.Status)

	var agentResp AgentResponse
	if err := json.NewDecoder(resp.Body).Decode(&agentResp); err != nil {
		log.Println("❌ Failed to decode agent response:", err)
		return nil, err
	}

	return &agentResp, nil
}
