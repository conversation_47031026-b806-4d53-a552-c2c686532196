package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"fin_fmcg_gateway/internal/config"
	"fin_fmcg_gateway/internal/router"
)

func main() {
	config.LoadEnv()

	srv := &http.Server{
		Addr:    fmt.Sprintf(":%s", os.Getenv("GATEWAY_PORT")),
		Handler: router.SetupRouter(),
	}

	// Graceful shutdown
	go func() {
		log.Printf("🚀 Gateway running on :%s\n", os.Getenv("GATEWAY_PORT"))
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to listen: %v", err)
		}
	}()

	// Wait for interrupt
	stop := make(chan os.Signal, 1)
	signal.Notify(stop, os.Interrupt, syscall.SIGTERM)
	<-stop

	log.Println("⏳ Shutting down gracefully...")
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := srv.Shutdown(ctx); err != nil {
		log.Fatalf("Server forced to shutdown: %v", err)
	}
	log.Println("✅ Gateway stopped.")
}
